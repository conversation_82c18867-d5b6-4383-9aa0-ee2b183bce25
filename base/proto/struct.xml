<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <struct name="LoginCommon" explain="登录公共信息">
        <field class="string" name="platform" explain="平台"/>
        <field class="string" name="ver" explain="客户端当前版本"/>
        <field class="string" name="distanceId" explain="ta访客id"/>
        <field class="string" name="os" explain="系统"/>
        <field class="bool" name="closeGuide" explain="关闭新手引导"/>
        <field class="string" name="inviteCode" explain="邀请码"/>
        <field class="string" name="packageSign" explain="包签名"/>
    </struct>
    <struct name="UserInfo" explain="用户账号数据信息">
        <field class="string" name="uid" explain="用户全局唯一id"/>
        <field class="string" name="lToken" explain="登录服token，后续用来免密登录"/>
        <field class="int32" name="age" explain="年龄"/>
        <field class="int32" name="signOutTime" explain="注销倒计时"/>
        <field class="string" name="nickName" explain="第三方用户名"/>
        <field class="string" name="avatarUrl" explain="第三方头像"/>
        <field class="string" name="openid" explain="第三方openid"/>
        <field class="string" name="userType" explain="用户类型"/>
    </struct>
    <struct name="ItemInfo" explain="物品基本数据">
        <field class="string" name="uid" explain="物品uid"/>
        <field class="int32" name="id" explain="物品配置"/>
        <field class="int32" name="num" explain="物品数量"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
    </struct>
    <struct name="TrainInfo" explain="列车数据">
        <field class="CarriageInfo" name="head" explain="车头"/>
        <array class="CarriageInfo" name="carriages" explain="车厢"/>
        <field class="int32" name="electricTime" explain="电力加成剩余时间"/>
        <field class="int32" name="waterTime" explain="水能加成剩余时间"/>
    </struct>
    <struct name="CarriageOutput" explain="车厢产出数据">
        <field class="int32" name="val" explain="当前累计"/>
        <field class="int64" name="accTotal" explain="总累计"/>
    </struct>
    <struct name="CarriageInfo" explain="车厢数据">
        <field class="int32" name="id" explain="车厢id"/>
        <field class="int32" name="themeLv" explain="车厢主题等级"/>
        <array class="TrainItemInfo" name="builds" explain="设施"/>
        <field class="int32" name="buildTime" explain="建造剩余时间"/>
        <field class="bool" name="openDoor" explain="是否打开猫猫门"/>
        <field class="CarriageOutput" name="starOutput" explain="星尘产出"/>
        <field class="CarriageOutput" name="heartOutput" explain="爱心产出"/>
        <field class="CarriageOutput" name="electricOutput" explain="电力产出"/>
        <field class="CarriageOutput" name="waterOutput" explain="水产出"/>
        <field class="int32" name="outputTime" explain="产出累计时间"/>
        <array class="CarriageGoodsInfo" name="goods" explain="已解锁货物"/>
        <field class="CarriageOutput" name="vitalityOutput" explain="元气值产出"/>
    </struct>
    <struct name="TrainItemInfo" explain="设施数据">
        <field class="int32" name="order" explain="设施序号"/>
        <field class="int32" name="lv" explain="设施等级"/>
        <field class="int32" name="skin" explain="设施皮肤"/>
    </struct>
    <struct name="Condition" explain="条件数据">
        <field class="int32" name="id" explain="条件id"/>
        <field class="int32" name="num" explain="条件数量"/>
        <field class="int32" name="type" explain="条件类型"/>
        <field class="bool" name="isHide" explain="是否隐藏"/>
        <field class="string" name="extra" explain="扩展数据"/>
    </struct>
    <struct name="Output" explain="产出数据">
        <field class="int32" name="id" explain="产出对象的id"/>
        <array class="Condition" name="items" explain="道具"/>
    </struct>
    <struct name="Energy" explain="加速能量数据">
        <field class="int32" name="energy" explain="加速能量"/>
        <field class="int32" name="costRecoverNum" explain="收费恢复次数"/>
        <field class="int32" name="freeRecoverNum" explain="免费恢复次数"/>
        <field class="bool" name="used" explain="使用过加速"/>
        <field class="bool" name="isSpeedUp" explain="是否正在加速"/>
        <field class="bool" name="isUnlockSpeedUpAuto" explain="是否解锁了自动加速"/>
    </struct>
    <struct name="PassengerInfo" explain="乘客数据">
        <field class="int32" name="id" explain="乘客配置id"/>
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="starLv" explain="星级"/>
        <field class="int32" name="exp" explain="经验"/>
        <field class="int32" name="dormId" explain="入住车厢"/>
        <field class="int32" name="heartOutput" explain="爱心产出"/>
        <field class="int32" name="starOutput" explain="星尘产出"/>
        <field class="int32" name="workId" explain="工作车厢id"/>
        <field class="int32" name="workIndex" explain="工位下标"/>
        <array class="PassengerPlot" name="plots" explain="剧情"/>
        <field class="int32" name="useSkinIndex" explain="穿戴的皮肤"/>
        <field class="int32" name="dormIndex" explain="坑位下标"/>
        <array class="PassengerTalent" name="talents" explain="天赋数据"/>
        <field class="map" name="profile" explain="资料数据">
            <key class="int32" explain="类型"/>
            <value class="int32" explain="位置"/>
        </field>
    </struct>
    <struct name="PassengerTalent" explain="乘客天赋数据">
        <field class="int32" name="id" explain="天赋id"/>
        <field class="int32" name="level" explain="等级"/>
    </struct>
    <struct name="PassengerPlot" explain="乘客剧情数据">
        <field class="string" name="id" explain="剧情id"/>
        <field class="bool" name="done" explain="是否已完成"/>
    </struct>
    <struct name="Planet" explain="星球数据">
        <field class="int32" name="id" explain="星球id"/>
        <field class="int32" name="curMapId" explain="当前地图id"/>
        <field class="int32" name="curNodeId" explain="当前节点id"/>
        <field class="double" name="nodeProgress" explain="当前节点进度"/>
        <field class="int32" name="nodePathProgress" explain="上个节点到当前节点进度"/>
        <field class="bool" name="landed" explain="是否登陆过星球"/>
        <field class="bool" name="reached" explain="是否到达过星球"/>
        <array class="PlanetBranch" name="branches" explain="星球支线"/>
        <field class="map" name="profileData" explain="资料数据">
            <key class="int32" explain="类型"/>
            <value class="int32" explain="位置"/>
        </field>
        <array class="int32" name="profileCollectReward" explain="领取的星球资料奖励区域数据"/>
        <field class="int32" name="roleNum" explain="宣传玩法人口数"/>
        <field class="int32" name="publicityUnGetOutputTime" explain="宣传玩法未收取时长累计"/>
        <field class="int32" name="publicityOutputTime" explain="宣传玩法已累计时间"/>
    </struct>
    <struct name="PlanetInfo" explain="探索数据">
        <field class="int32" name="curPlanetId" explain="当前星球id"/>
        <field class="int32" name="moveTargetId" explain="目标星球id"/>
        <field class="int32" name="moveSurplusTime" explain="航行结束剩余时间>=0"/>
        <array class="Planet" name="planets" explain="星球数据"/>
        <field class="RageMode" name="rageMode" explain="狂暴模式"/>
    </struct>
    <struct name="PlanetBranch" explain="星球支线">
        <field class="string" name="id" explain="支线id"/>
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="int32" name="nodeId" explain="当前节点id"/>
        <array class="int32" name="nodeRewards" explain="当前节点已领取奖励"/>
    </struct>
    <struct name="RageMode" explain="狂暴模式">
        <field class="string" name="id" explain="狂暴模式节点id"/>
        <field class="int32" name="count" explain="狂暴模式次数"/>
    </struct>
    <struct name="ToolInfo" explain="工具数据信息">
        <field class="int32" name="type" explain="工具类型"/>
        <field class="int32" name="lv" explain="工具等级"/>
    </struct>
    <struct name="TaskTarget" explain="任务目标数据">
        <field class="string" name="id" explain="当前目标id"/>
        <field class="int32" name="num" explain="当前目标数量"/>
    </struct>
    <struct name="Task" explain="任务数据">
        <field class="string" name="id" explain="当前任务id"/>
        <array class="TaskTarget" name="targets" explain="任务目标进度"/>
    </struct>
    <struct name="TaskInfo" explain="任务模块数据">
        <array class="Task" name="tasks" explain="当前任务"/>
        <array class="string" name="completes" explain="已完成的任务Id"/>
    </struct>
    <struct name="ToolModel" explain="打造台数据">
        <field class="int32" name="Lv" explain="当前等级"/>
        <field class="map" name="tools" explain="工具集合">
            <key class="int32" explain="工具类型"/>
            <value class="ToolInfo" explain="工具数据"/>
        </field>
        <field class="int32" name="blessCount" explain="祝福次数"/>
        <field class="string" name="blessId" explain="祝福id"/>
    </struct>
    <struct name="Player" explain="基本的角色数据信息">
        <field class="string" name="uid" explain="角色id"/>
        <field class="string" name="nickName" explain="角色名称"/>
        <field class="string" name="avatarUrl" explain="角色头像"/>
        <field class="fixed64" name="createTime" explain="创建时间"/>
        <field class="fixed64" name="offlineTime" explain="离线时间"/>
        <field class="fixed64" name="totalOnlineTime" explain="总在线时长"/>
        <field class="int32" name="diamond" explain="钻石"/>
        <field class="int32" name="heart" explain="爱心"/>
        <field class="int32" name="starDust" explain="星尘"/>
        <field class="int32" name="paperCrane" explain="纸鹤"/>
        <field class="bool" name="gm" explain="是不是gm"/>
        <field class="fixed64" name="time" explain="游戏时长"/>
        <field class="Energy" name="energy" explain="加速体力数据"/>
        <array class="ItemInfo" name="bag" explain="背包物品数据"/>
        <field class="TrainInfo" name="train" explain="列车数据"/>
        <array class="PassengerInfo" name="passengers" explain="乘客数据"/>
        <field class="PlanetInfo" name="planetInfo" explain="探索数据"/>
        <field class="int32" name="guideId" explain="当前引导模块id"/>
        <array class="GuideInfo" name="guideInfo" explain="引导数据"/>
        <field class="TaskInfo" name="taskInfo" explain="任务数据"/>
        <field class="ToolModel" name="toolModel" explain="打造台数据"/>
        <field class="Explore" name="explore" explain="探索数据"/>
        <field class="Wanted" name="wanted" explain="悬赏数据"/>
        <field class="int32" name="nextDaySurpluTime" explain="下次每日刷新剩余时间"/>
        <array class="MailInfo" name="mailList" explain="邮件列表(只有基本状态)"/>
        <field class="AchievementInfo" name="achievementInfo" explain="成就任务数据"/>
        <field class="int32" name="changeNameCnt" explain="改名次数"/>
        <array class="NewMarkInfo" name="newMarkList" explain="new标签数据"/>
        <field class="Chest" name="chest" explain="宝箱模块"/>
        <field class="int32" name="heartOutput" explain="爱心产出"/>
        <field class="int32" name="passengerStarOutput" explain="乘客小费产出"/>
        <field class="Tower" name="tower" explain="爬塔模块"/>
        <field class="BlackHole" name="blackHole" explain="黑洞模块"/>
        <field class="Battle" name="battle" explain="战斗模块"/>
        <field class="Resonance" name="resonance" explain="共鸣"/>
        <field class="Equip" name="equip" explain="装备模块"/>
        <field class="Instance" name="instance" explain="副本模块"/>
        <field class="int32" name="passengerRestCdTime" explain="乘客重置cd"/>
        <field class="Store" name="store" explain="商店数据"/>
        <field class="map" name="skin" explain="皮肤数据">
            <key class="int32" explain="乘客id"/>
            <value class="PassengerSkinData" explain="乘客对应皮肤信息"/>
        </field>
        <field class="Jackpot" name="jackpot" explain="抽卡模块数据"/>
        <field class="Pay" name="pay" explain="支付模块数据"/>
        <field class="Transport" name="transport" explain="护送模块数据"/>
        <field class="map" name="configMd5" explain="服务器配置md5，仅仅dev下发">
            <key class="string" explain="文件名称"/>
            <value class="string" explain="md5值"/>
        </field>
        <field class="Field" name="field" explain="农场模块数据"/>
        <field class="map" name="frag" explain="投影数据">
            <key class="int32" explain="投影id"/>
            <value class="int32" explain="数量"/>
        </field>
        <field class="Ore" name="ore" explain="矿场模块数据"/>
        <field class="Collect" name="collect" explain="收集玩法模块数据"/>
        <field class="ArrestModule" name="arrest" explain="通缉令模块数据"/>
        <field class="int32" name="nextWeekSurplusTime" explain="下次周刷新时间"/>
        <field class="SpaceStone" name="spaceStone" explain="空间石"/>
        <field class="DailyTaskInfo" name="dailyTask" explain="每日任务"/>
        <array class="int32" name="passengerProfiles" explain="乘客资料"/>
        <array class="int32" name="planetProfiles" explain="星球资料"/>
        <field class="int32" name="offlineRewardTime" explain="离线奖励时长"/>
        <field class="int32" name="offsetTime" explain="偏移时间（debug环境使用）"/>
        <field class="PvpModuleData" name="pvpModuleData" explain="pvp数据"/>
        <field class="Ad" name="ad" explain="广告数据"/>
        <field class="ProfileBranch" name="profileBranch" explain="记忆阁"/>
        <field class="TrainDailyTask" name="trainDailyTask" explain="列车任务"/>
        <field class="BurstTask" name="burstTask" explain="突发任务"/>
        <field class="TrainActivity" name="trainActivity" explain="列车活动"/>
        <field class="TechData" name="techData" explain="科技数据"/>
    </struct>
    <struct name="PassengerSkinData" explain="乘客对应皮肤信息组">
        <array class="PassengerSkin" name="list" explain="皮肤列表"/>
    </struct>
    <struct name="PassengerSkin" explain="乘客对应皮肤信息">
        <field class="int32" name="index" explain="皮肤序号"/>
    </struct>
    <struct name="CurrencyInfo" explain="货币信息">
        <field class="int32" name="type" explain="货币类型 1钻石 2星尘 3爱心 4纸鹤"/>
        <field class="int32" name="val" explain="货币值"/>
    </struct>
    <struct name="GuideInfo" explain="引导数据信息">
        <field class="int32" name="id" explain="模块id"/>
        <array class="int32" name="keySteps" explain="已经完成的关键步骤"/>
    </struct>
    <struct name="MailInfo" explain="邮件数据">
        <field class="string" name="id" explain="id"/>
        <field class="fixed64" name="time" explain="创建时间"/>
        <field class="string" name="title" explain="邮件标题"/>
        <field class="string" name="content" explain="邮件内容"/>
        <array class="Condition" name="rewards" explain="附件"/>
        <field class="bool" name="read" explain="是否读取"/>
        <field class="bool" name="attach" explain="是否领取"/>
    </struct>
    <struct name="Monster" explain="怪物数据">
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="lv" explain="等级"/>
    </struct>
    <struct name="Wanted" explain="悬赏模块数据">
        <array class="WantedInfo" name="list" explain="悬赏列表"/>
    </struct>
    <struct name="WantedInfo" explain="悬赏数据">
        <field class="int32" name="name" explain="名字下标"/>
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="people" explain="需求人数"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
        <field class="int32" name="state" explain="状态"/>
        <array class="Condition" name="rewards" explain="奖励"/>
        <array class="WantedCondition" name="conditions" explain="条件"/>
        <array class="int32" name="roles" explain="进行中的角色"/>
        <field class="int32" name="publisher" explain="发布者"/>
        <field class="int32" name="type" explain="类型"/>
        <field class="int32" name="bg" explain="主要事件"/>
        <field class="int32" name="bgArg" explain="事件参数"/>
    </struct>
    <struct name="WantedCondition" explain="悬赏数据">
        <field class="int32" name="type" explain="类型"/>
        <field class="int32" name="value" explain="值"/>
    </struct>
    <struct name="AchievementInfo" explain="成就任务数据">
        <array class="Task" name="tasks" explain="当前成就任务"/>
        <array class="string" name="completes" explain="已完成的成就任务Id"/>
        <array class="fixed64" name="completeTime" explain="完成任务时间"/>
    </struct>
    <struct name="NewMarkInfo" explain="new标签">
        <field class="int32" name="typeId" explain="类型"/>
        <array class="int32" name="aryVal" explain="数据"/>
    </struct>
    <struct name="Storage" explain="仓库">
        <field class="int32" name="Type" explain="类型"/>
        <field class="int32" name="Lv" explain="等级"/>
    </struct>
    <struct name="BoxInfo" explain="宝箱">
        <field class="int32" name="id" explain="宝箱id"/>
        <field class="int32" name="num" explain="宝箱数量"/>
    </struct>
    <struct name="BoxInfoArray" explain="宝箱数据集">
        <array class="BoxInfo" name="data" explain="数据"/>
    </struct>
    <struct name="Chest" explain="宝箱模块">
        <field class="int32" name="medal" explain="总积分"/>
        <field class="int32" name="step" explain="当前档位"/>
        <field class="map" name="data" explain="宝箱集合">
            <key class="int32" explain="宝箱的ItemType"/>
            <value class="BoxInfoArray" explain="工具数据"/>
        </field>
    </struct>
    <struct name="Tower" explain="爬塔">
        <field class="string" name="checkPointId" explain="当前关卡id"/>
        <field class="bool" name="isDone" explain="已完成"/>
    </struct>
    <struct name="BlackHole" explain="黑洞">
        <field class="string" name="curId" explain="当前节点id"/>
        <field class="string" name="nextId" explain="下个节点id"/>
        <array class="BlackHoleNode" name="map" explain="地图"/>
        <array class="BlackHoleBuff" name="buffs" explain="buff"/>
        <array class="BattleRole" name="roles" explain="选择的角色"/>
        <array class="BattleRole" name="aids" explain="援助"/>
        <array class="string" name="deads" explain="已阵亡的角色"/>
        <field class="BattleTeam" name="team" explain="编队"/>
        <field class="int32" name="level" explain="当前难度"/>
        <array class="BlackHoleBoss" name="bosses" explain="不同难度的boss队伍"/>
        <array class="BlackHoleEquip" name="equips" explain="科技装备"/>
        <field class="int32" name="currency" explain="星海币"/>
        <field class="bool" name="isUnlock" explain="是否解锁"/>
        <field class="double" name="add" explain="科技装备加成"/>
    </struct>
    <struct name="BlackHoleNode" explain="黑洞节点">
        <field class="string" name="id" explain="节点id"/>
        <field class="int32" name="type" explain="节点类型"/>
        <array class="BattleRole" name="enemies" explain="敌人"/>
        <array class="BlackHoleBuff" name="buffs" explain="buff"/>
        <array class="BattleRole" name="aids" explain="援助"/>
        <field class="bool" name="isFog" explain="是否为迷雾"/>
        <array class="Condition" name="reward" explain="奖励"/>
        <array class="BlackHoleEquip" name="equips" explain="科技装备"/>
    </struct>
    <struct name="BlackHoleBuff" explain="黑洞buff">
        <field class="int32" name="index" explain="buff下标"/>
        <field class="int32" name="type" explain="buff类型"/>
        <array class="string" name="targets" explain="作用uid"/>
        <field class="map" name="add" explain="宝箱集合">
            <key class="string" explain=""/>
            <value class="int32" explain=""/>
        </field>
    </struct>
    <struct name="BlackHoleEquip" explain="黑洞科技装备">
        <field class="int32" name="id" explain="装备id"/>
        <field class="int32" name="target" explain="作用目标"/>
        <field class="int32" name="level" explain="等级"/>
    </struct>
    <struct name="BlackHoleBoss" explain="黑洞boss">
        <field class="int32" name="level" explain="难度"/>
        <array class="BattleRole" name="roles" explain="角色"/>
    </struct>
    <struct name="BattleRole" explain="战斗角色">
        <field class="string" name="uid" explain="角色uid"/>
        <field class="int32" name="id" explain="角色id"/>
        <field class="int32" name="type" explain="乘客=1还是怪物=2"/>
        <field class="int32" name="lv" explain="角色等级"/>
        <field class="int32" name="starLv" explain="角色星级"/>
        <field class="int32" name="attack" explain="角色攻击"/>
        <field class="int32" name="hp" explain="角色血量"/>
        <array class="PassengerTalent" name="talents" explain="天赋数据"/>
        <array class="EquipItem" name="equips" explain="装备"/>
        <field class="double" name="attrRate" explain="属性倍率"/>
    </struct>
    <struct name="BattleResult" explain="战斗角色">
        <field class="bool" name="isWin" explain="我方是否胜利"/>
        <array class="string" name="uids" explain="存活角色uid"/>
    </struct>
    <struct name="BattleTeam" explain="战斗编队">
        <field class="int32" name="id" explain="编队id"/>
        <array class="string" name="uids" explain="角色"/>
    </struct>
    <struct name="Battle" explain="战斗编队">
        <array class="BattleTeam" name="teams" explain="队伍"/>
    </struct>
    <struct name="Equip" explain="装备信息">
        <array class="EquipItem" name="data" explain="装备列表"/>
        <field class="map" name="proficiency" explain="打造台熟练度">
            <key class="string" explain="品质-部位"/>
            <value class="int32" explain="熟练度"/>
        </field>
    </struct>
    <struct name="EquipEffect" explain="装备词条">
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="attr" explain="属性"/>
        <field class="int32" name="level" explain="等级"/>
    </struct>
    <struct name="EquipItem" explain="装备信息">
        <field class="string" name="uid" explain="唯一id"/>
        <field class="int32" name="id" explain="配置id"/>
        <field class="int32" name="level" explain="等级"/>
        <array class="EquipEffect" name="effects" explain="词条"/>
        <field class="bool" name="used" explain="是否穿戴中"/>
    </struct>
    <struct name="Instance" explain="每日副本信息">
        <field class="int32" name="level" explain="最后一个通关的关卡"/>
        <field class="bool" name="isUnlock" explain="是否解锁"/>
        <field class="bool" name="isCompletePuzzle" explain="是否完成解密"/>
    </struct>
    <struct name="Store" explain="商店模块数据">
        <array class="StoreInfo" name="list" explain="商店数据列表"/>
    </struct>
    <struct name="StoreInfo" explain="商店数据">
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="refreshCount" explain="已刷新次数"/>
        <array class="Goods" name="goods" explain="商品"/>
        <field class="int32" name="refreshTime" explain="下次刷新剩余时间"/>
    </struct>
    <struct name="Goods" explain="商品数据">
        <field class="int32" name="stock" explain="库存"/>
        <field class="Condition" name="item" explain="道具"/>
        <field class="int32" name="discount" explain="折扣"/>
        <field class="Condition" name="cost" explain="消耗的货币，num取打折后的值"/>
    </struct>
    <struct name="Jackpot" explain="抽卡模块数据">
        <field class="int32" name="jackpotDailyNum" explain="当日抽卡次数"/>
        <field class="int32" name="jackpotTotalCount" explain="总累计抽卡次数"/>
        <field class="int32" name="jackpotPoints" explain="抽卡积分"/>
    </struct>
    <struct name="Pay" explain="支付数据">
        <array class="NotFinishPayOrder" name="notFinishOrders" explain="未完成订单列表"/>
        <field class="map" name="payCountMap" explain="部分商品的购买次数">
            <key class="string" explain="商品id"/>
            <value class="int32" explain="次数"/>
        </field>
    </struct>
    <struct name="NotFinishPayOrder" explain="未完成订单数据">
        <field class="string" name="cpOrderId" explain="订单id"/>
        <field class="string" name="productId" explain="产品id"/>
        <field class="int32" name="quantity" explain="数量"/>
        <field class="string" name="platform" explain="平台"/>
    </struct>
    <struct name="CarriageGoodsInfo" explain="餐厅菜单数据">
        <field class="string" name="id" explain="id"/>
        <field class="int32" name="lv" explain="等级"/>
    </struct>
    <struct name="Resonance" explain="共鸣数据">
        <array class="ResonanceSlot" name="slots" explain="槽位"/>
    </struct>
    <struct name="ResonanceSlot" explain="共鸣槽数据">
        <field class="int32" name="id" explain="乘客id"/>
        <field class="int32" name="cd" explain="冷却时间"/>
    </struct>
    <struct name="Transport" explain="护送模块数据">
        <field class="int32" name="exp" explain="当前经验"/>
        <array class="TransportData" name="list" explain=""/>
    </struct>
    <struct name="TransportData" explain="单个护送数据">
        <field class="int32" name="starLv" explain="星级"/>
        <field class="int32" name="start" explain="起点星球"/>
        <field class="int32" name="end" explain="终点星球"/>
        <array class="Condition" name="rewards" explain="奖励数据"/>
        <array class="Condition" name="fixRewards" explain="额外必得奖励数据"/>
        <field class="enum.TransportDataState" name="state" explain="数据状态"/>
        <field class="int32" name="load" explain="任务负重要求"/>
        <field class="bool" name="rare" explain="是不是稀有任务"/>
        <field class="bool" name="timeStoneKey" explain="是不是时间之钥任务"/>
        <field class="int32" name="actor" explain="委托人"/>
        <field class="string" name="key" explain="委托对话"/>
        <field class="TransportBattleData" name="battleData" explain="海盗数据"/>
    </struct>
    <struct name="TransportBattleData" explain="护送的战斗数据">
        <array class="BattleRole" name="monsters" explain="敌人"/>
        <field class="int32" name="second" explain="第多少s触发战斗"/>
    </struct>
    <struct name="Field" explain="农场数据">
        <array class="FieldCeil" name="ceilData" explain="格子数据列表"/>
        <array class="Condition" name="seedData" explain="种子数据列表"/>
        <field class="int32" name="Level" explain="等级"/>
        <field class="map" name="levelCond" explain="升级条件数据">
            <key class="string" explain="条件type"/>
            <value class="int32" explain="达成数量"/>
        </field>
    </struct>
    <struct name="Ore" explain="矿场数据">
        <field class="int32" name="recoverTime" explain="下一个镐子回复时间"/>
        <field class="map" name="oreItems" explain="矿石数据">
            <key class="int32" explain="id"/>
            <value class="int32" explain="数量"/>
        </field>
        <array class="OreLevelData" name="data" explain="矿洞数据"/>
        <field class="bool" name="isUnlock" explain="是否解锁"/>
    </struct>
    <struct name="OreLevelData" explain="矿场难度对应的数据">
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="depth" explain="当前所在深度"/>
        <array class="OreRowData" name="data" explain="格子数据"/>
        <field class="bool" name="isSpecialArea" explain="是否处于特殊区域层"/>
    </struct>
    <struct name="OreRowData" explain="矿场单行数据">
        <array class="OreCeilData" name="data" explain="行数据"/>
    </struct>
    <struct name="OreCeilData" explain="矿场单格数据">
        <field class="enum.OreCeilType" name="type" explain="格子类型"/>
        <array class="Condition" name="oreExtra" explain="如果是矿石格子,这个是矿石数据,怪物格子就是奖励数据"/>
        <array class="BattleRole" name="monsters" explain="如果是怪物格子,这个是战斗数据"/>
        <array class="Point" name="ref" explain="格子引用"/>
        <field class="enum.OrePageNextType" name="nextType" explain="下一层类型"/>
        <field class="enum.OreBlockItemDrillDirection" name="direction" explain="钻头方向"/>
    </struct>
    <struct name="FieldCeil" explain="农场格子数据">
        <field class="int32" name="id" explain="格子id"/>
        <field class="int32" name="type" explain="格子类型"/>
        <field class="int32" name="surplusTime" explain="生长剩余时间"/>
        <field class="enum.FieldCeilState" name="state" explain="状态"/>
        <field class="int32" name="plantId" explain="作物id"/>
    </struct>
    <struct name="Point" explain="点位">
        <field class="int32" name="x" explain="x"/>
        <field class="int32" name="y" explain="y"/>
    </struct>
    <struct name="Collect" explain="采集玩法">
        <array class="MapMineItemData" name="mine" explain="地图采集物数据"/>
    </struct>
    <struct name="DailyTaskInfo" explain="每日任务模块数据">
        <array class="DailyTask" name="tasks" explain="任务列表"/>
        <field class="bool" name="bigGet" explain="是否领取大奖励"/>
    </struct>
    <struct name="DailyTask" explain="每日任务">
        <field class="string" name="uid" explain="任务uid"/>
        <field class="int32" name="id" explain="任务id"/>
        <array class="Condition" name="target" explain="目标"/>
        <array class="Condition" name="progress" explain="进度"/>
        <array class="Condition" name="reward" explain="奖励"/>
        <field class="int32" name="state" explain="状态"/>
        <field class="int32" name="sender" explain="任务发起者"/>
        <field class="int32" name="content" explain="任务lang"/>
        <field class="int32" name="planet" explain="战斗所在星球"/>
        <array class="BattleRole" name="battleInfo" explain="战斗数据"/>
    </struct>
    <struct name="MapMineItemData" explain="地图采集物数据">
        <field class="int32" name="id" explain="采集物id"/>
        <field class="int32" name="type" explain="采集物类型"/>
        <array class="Condition" name="reward" explain="奖励"/>
        <field class="Point" name="position" explain="位置"/>
        <field class="string" name="uid" explain="唯一id"/>
        <field class="double" name="scale" explain="缩放比例"/>
    </struct>
    <struct name="TimeStoneRecord" explain="时间宝石记录列表">
        <array class="TimeStoneRecordData" name="records" explain=""/>
    </struct>
    <struct name="TimeStoneRecordData" explain="时间宝石记录数据">
        <field class="string" name="id" explain="id"/>
        <field class="string" name="type" explain="type"/>
        <field class="string" name="arg" explain="参数"/>
        <field class="bool" name="use" explain="是否回溯"/>
    </struct>
    <struct name="ArrestModule" explain="通缉令列表数据">
        <array class="Arrest" name="arrests" explain="列表"/>
        <field class="int32" name="score" explain="声望"/>
        <field class="ArrestResult" name="result" explain="上期战报"/>
        <field class="int32" name="currency" explain="货币"/>
    </struct>
    <struct name="Arrest" explain="通缉令数据">
        <field class="string" name="id" explain="id"/>
        <field class="int32" name="expirationTime" explain="过期时间"/>
        <field class="int32" name="planetId" explain="星球id"/>
        <field class="int32" name="star" explain="星级"/>
        <field class="enum.ArrestState" name="state" explain="状态"/>
        <array class="BattleRole" name="monsters" explain="怪物数据"/>
        <array class="Condition" name="rewards" explain="奖励"/>
        <field class="int32" name="storyId" explain="罪名id"/>
        <array class="ArrestClue" name="clues" explain="线索"/>
    </struct>
    <struct name="ArrestClue" explain="通缉令线索">
        <field class="enum.ArrestClueType" name="type" explain="类型"/>
        <array class="int32" name="planets" explain="星球id"/>
        <field class="bool" name="isHideInfo" explain="是否隐藏信息"/>
        <field class="enum.ArrestPlaceType" name="placeType" explain="地点类型"/>
        <field class="enum.ArrestTimeType" name="timeType" explain="时间类型"/>
    </struct>
    <struct name="ArrestResult" explain="通缉战报">
        <array class="ArrestResultDetail" name="wins" explain="已完成"/>
        <array class="ArrestResultDetail" name="fails" explain="未完成"/>
        <field class="int32" name="score" explain="声望变化"/>
    </struct>
    <struct name="ArrestResultDetail" explain="通缉战报详情">
        <field class="int32" name="star" explain="星级"/>
        <field class="int32" name="count" explain="数量"/>
    </struct>
    <struct name="SpaceStone" explain="空间宝石">
        <array class="int32" name="marks" explain="标记id"/>
        <field class="int32" name="lv" explain="等级"/>
        <field class="int32" name="energy" explain="剩余能量"/>
    </struct>
    <struct name="PvpModuleData" explain="玩家pvp模块数据">
        <field class="map" name="ticket" explain="挑战次数数据">
            <key class="int32" explain="竞技场类型"/>
            <value class="int32" explain="次数"/>
        </field>
        <field class="map" name="duration" explain="持续时间数据">
            <key class="int32" explain="竞技场类型"/>
            <value class="int32" explain="持续时间"/>
        </field>
    </struct>
    <struct name="PvpNormalData" explain="玩家普通pvp数据">
        <field class="int32" name="score" explain="积分"/>
        <field class="int32" name="rank" explain="排名"/>
        <array class="BattleRole" name="battleRoles" explain="阵容"/>
    </struct>
    <struct name="SimplePlayerData" explain="玩家简单数据">
        <field class="string" name="id" explain="id"/>
        <field class="string" name="name" explain="名字"/>
        <field class="string" name="head" explain="头像"/>
    </struct>
    <struct name="PvpSimplePlayerData" explain="玩家简单pvp数据">
        <field class="int32" name="score" explain="积分"/>
        <field class="int32" name="rank" explain="排名"/>
        <field class="SimplePlayerData" name="ext" explain="简要数据"/>
        <array class="BattleRole" name="battleRoles" explain="阵容"/>
    </struct>
    <struct name="PvpBattleRecordData" explain="pvp战绩数据">
        <field class="string" name="docId" explain="文档id"/>
        <field class="PvpSimplePlayerData" name="attacker" explain="进攻方"/>
        <field class="PvpSimplePlayerData" name="defender" explain="防守方"/>
        <field class="int32" name="result" explain="0平1胜2负"/>
        <field class="int32" name="time" explain="多久之前发生的战斗"/>
        <field class="int32" name="scoreChange" explain="积分变动"/>
        <field class="int32" name="score1" explain="进攻方原始积分"/>
        <field class="int32" name="score2" explain="防守方原始积分"/>
    </struct>
    <struct name="ExploreTeam" explain="探索队伍数据">
        <field class="int32" name="planetId" explain="星球ID"/>
        <array class="int32" name="roles" explain="探索的乘客ID列表"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
        <array class="Condition" name="rewards" explain="探索奖励"/>
    </struct>
    <struct name="Explore" explain="探索模块数据">
        <array class="ExploreTeam" name="teams" explain="探索队伍列表"/>
    </struct>
    <struct name="Ad" explain="广告模块数据">
        <field class="map" name="data" explain="广告数据">
            <key class="int32" explain="广告类型"/>
            <value class="int32" explain="观看次数"/>
        </field>
    </struct>
    <struct name="ProfileBranch" explain="记忆阁">
        <field class="int32" name="id" explain="当前关卡id"/>
        <array class="ProfileBranchLevel" name="levels" explain="关卡列表"/>
        <field class="int32" name="energy" explain="体力"/>
        <field class="int32" name="surplusTime" explain="体力剩余时间"/>
    </struct>
    <struct name="ProfileBranchLevel" explain="记忆阁关卡">
        <field class="int32" name="id" explain="关卡id"/>
        <field class="int32" name="nodeId" explain="当前节点id"/>
        <array class="MapMineItemData" name="nodes" explain="节点列表"/>
        <field class="bool" name="unlock" explain="是否解锁"/>
    </struct>
    <struct name="TrainDailyTask" explain="列车任务">
        <array class="TrainDailyTaskItem" name="list" explain="任务列表"/>
    </struct>
    <struct name="TrainDailyTaskItem" explain="列车任务">
        <field class="int32" name="id" explain="任务id"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
        <field class="enum.CommonState" name="state" explain="状态"/>
        <array class="Condition" name="rewards" explain="奖励"/>
        <array class="WantedCondition" name="conditions" explain="条件"/>
        <array class="int32" name="roles" explain="进行中的角色"/>
        <field class="int32" name="people" explain="限定数量"/>
        <field class="int32" name="trainId" explain="限定车厢"/>
        <field class="int32" name="level" explain="星级"/>
    </struct>
    <struct name="BurstTask" explain="突发任务">
        <array class="BurstTaskItem" name="list" explain="任务列表"/>
    </struct>
    <struct name="BurstTaskItem" explain="突发任务">
        <field class="int32" name="id" explain="任务id"/>
        <field class="int32" name="surplusTime" explain="剩余时间"/>
        <field class="enum.CommonState" name="state" explain="状态"/>
        <array class="Condition" name="rewards" explain="奖励"/>
        <array class="WantedCondition" name="conditions" explain="条件"/>
        <array class="int32" name="roles" explain="进行中的角色"/>
        <field class="int32" name="people" explain="限定数量"/>
        <field class="int32" name="trainId" explain="限定车厢"/>
    </struct>
    <struct name="TrainActivity" explain="列车活动">
        <array class="TrainActivityItem" name="list" explain="任务列表"/>
        <field class="int64" name="arrangeWorldTime" explain="安排时的世界时间"/>
    </struct>
    <struct name="TrainActivityItem" explain="列车活动数据">
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="cfgId" explain="配置id"/>
        <field class="int32" name="trainId" explain="表现作用车厢id"/>
        <array class="Condition" name="rewards" explain="奖励"/>
        <field class="int64" name="surplusTime" explain="剩余时间"/>
        <field class="int32" name="costDay" explain="消耗天数"/>
        <field class="enum.CommonState" name="state" explain="状态"/>
    </struct>
    <struct name="TechData" explain="科技数据">
        <field class="map" name="data" explain="科技数据">
            <key class="int32" explain="科技id"/>
            <value class="int32" explain="科技等级"/>
        </field>
    </struct>
</messages>