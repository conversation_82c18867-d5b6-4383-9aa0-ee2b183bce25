package lua

import (
	"fmt"
	"os"
)

var (
	AllocateAreaScript   = ""
	UpdatePvpScoreScript = ""
)

func InitRedisScript() {
	AllocateAreaScript = load("alloacate_area.lua")
	UpdatePvpScoreScript = load("update_pvp_score.lua")
}

func load(url string) string {
	bytes, err := os.ReadFile(fmt.Sprintf("bin/conf/lua/%s", url))
	if err != nil {
		panic(err)
	}
	v := string(bytes)
	if v == "" {
		panic("lua script is empty")
	}
	return v
}
